.utopiaContainer {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  perspective: 1000px;
}

.sun {
  position: absolute;
  top: 10%;
  left: 20%;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(255, 249, 224, 0.8) 0%, rgba(255, 249, 224, 0) 70%);
  border-radius: 50%;
  box-shadow: 0 0 60px 30px rgba(255, 249, 224, 0.3);
  animation: drift 60s linear infinite alternate;
}

@keyframes drift {
  from {
    transform: translate(-10%, -10%);
  }
  to {
    transform: translate(10%, 10%);
  }
}

.structure {
  position: absolute;
  bottom: 0;
  background: linear-gradient(to top, rgba(200, 230, 201, 0.3), rgba(173, 232, 244, 0.1));
  border-top: 2px solid rgba(255, 255, 255, 0.4);
  border-radius: 10px 10px 0 0;
}

.structure1 {
  left: 15%;
  width: 10%;
  height: 60%;
  transform: skewX(-10deg);
}

.structure2 {
  left: 45%;
  width: 15%;
  height: 80%;
}

.structure3 {
  right: 15%;
  width: 12%;
  height: 50%;
  transform: skewX(10deg);
}

.vines {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.vinePath1,
.vinePath2,
.vinePath3 {
  fill: none;
  stroke: #4ade80;
  stroke-width: 3;
  stroke-linecap: round;
  stroke-dasharray: 500;
  stroke-dashoffset: 500;
  animation: grow 15s ease-out forwards;
  filter: drop-shadow(0 0 5px #4ade80);
}

.vinePath2 {
  animation-delay: 2s;
}

.vinePath3 {
  animation-delay: 4s;
}

@keyframes grow {
  to {
    stroke-dashoffset: 0;
  }
}

.particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  bottom: -20px;
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  animation: floatUp var(--duration, 20s) infinite linear;
  box-shadow: 0 0 8px 2px rgba(255, 255, 255, 0.5);
}

@keyframes floatUp {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  25% {
    transform: translateY(-25vh) translateX(calc(var(--x-drift) * 0.3));
  }
  50% {
    transform: translateY(-50vh) translateX(calc(var(--x-drift) * 0.7));
  }
  75% {
    transform: translateY(-75vh) translateX(calc(var(--x-drift) * 0.4));
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) translateX(var(--x-drift));
    opacity: 0;
  }
}
