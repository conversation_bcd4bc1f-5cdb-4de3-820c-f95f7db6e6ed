import React from 'react';
import styles from './UtopiaShapes.module.css';

const UtopiaShapes: React.FC = () => {
  const particleCount = 30;

  return (
    <div className={styles.utopiaContainer}>
      <div className={styles.sun}></div>

      <div className={`${styles.structure} ${styles.structure1}`}></div>
      <div className={`${styles.structure} ${styles.structure2}`}></div>
      <div className={`${styles.structure} ${styles.structure3}`}></div>

      <svg className={styles.vines} viewBox="0 0 400 400" preserveAspectRatio="xMidYMid slice">
        <path className={styles.vinePath1} d="M 70,400 C 80,300 100,250 80,150" />
        <path className={styles.vinePath2} d="M 200,400 C 180,300 220,200 200,50" />
        <path className={styles.vinePath3} d="M 330,400 C 320,300 350,250 340,200" />
      </svg>

      <div className={styles.particles}>
        {Array.from({ length: particleCount }).map((_, i) => {
          const style = {
            left: `${Math.random() * 100}%`,
            animationDuration: `${15 + Math.random() * 15}s`, // Slower, more graceful
            animationDelay: `${Math.random() * 15}s`,
            '--x-drift': `${(Math.random() - 0.5) * 200}px`, // Wider sway
          } as React.CSSProperties;
          return <div key={i} className={styles.particle} style={style}></div>;
        })}
      </div>
    </div>
  );
};

export default UtopiaShapes;
