import React from 'react';
import styles from './DystopiaShapes.module.css';

const DystopiaShapes: React.FC = () => {
  const rainDropCount = 100;
  const vehicleCount = 5;

  return (
    <div className={styles.dystopiaContainer}>
      <div className={styles.rain}>
        {Array.from({ length: rainDropCount }).map((_, i) => (
          <div
            key={i}
            className={styles.rainDrop}
            style={{
              left: `${Math.random() * 100}%`,
              animationDuration: `${0.5 + Math.random() * 0.5}s`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>

      <div className={styles.cityscape}>
        <div className={`${styles.building} ${styles.building1}`}></div>
        <div className={`${styles.building} ${styles.building2}`}></div>
        <div className={`${styles.building} ${styles.building3}`}></div>
        <div className={`${styles.building} ${styles.building4}`}></div>
      </div>

      <div className={styles.neonSigns}>
        <div className={`${styles.neonSign} ${styles.neonSign1}`}>CYBERCORP</div>
        <div className={`${styles.neonSign} ${styles.neonSign2}`}>NEXUS</div>
      </div>

      <div className={styles.vehicles}>
        {Array.from({ length: vehicleCount }).map((_, i) => (
          <div
            key={i}
            className={styles.vehicle}
            style={{
              top: `${10 + Math.random() * 50}%`,
              animationDuration: `${3 + Math.random() * 4}s`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default DystopiaShapes;
