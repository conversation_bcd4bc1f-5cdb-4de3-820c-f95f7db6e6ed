.dystopiaContainer {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #0c0a09; /* Dark, polluted sky */
}

.rain {
  position: absolute;
  width: 100%;
  height: 100%;
}

.rainDrop {
  position: absolute;
  bottom: 100%;
  width: 1px;
  height: 50px;
  background: linear-gradient(to top, rgba(100, 180, 255, 0), rgba(100, 180, 255, 0.4));
  animation: fall linear infinite;
}

@keyframes fall {
  to {
    transform: translateY(100vh);
  }
}

.cityscape {
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 0;
}

.building {
  position: absolute;
  bottom: 0;
  background-color: #1a1a1d;
  border-top: 1px solid #4b5563;
}

.building1 { left: 5%; width: 20%; height: 70%; }
.building2 { left: 30%; width: 15%; height: 90%; }
.building3 { right: 25%; width: 25%; height: 60%; }
.building4 { right: 5%; width: 18%; height: 80%; }

.neonSigns {
  position: absolute;
  width: 100%;
  height: 100%;
}

.neonSign {
  position: absolute;
  font-family: 'Orbitron', sans-serif; /* A futuristic font */
  font-size: 3rem;
  font-weight: bold;
  text-transform: uppercase;
  animation: flicker 1.5s infinite alternate;
}

.neonSign1 {
  top: 20%;
  left: 8%;
  color: #f0f;
  text-shadow: 0 0 5px #f0f, 0 0 10px #f0f, 0 0 20px #f0f, 0 0 40px #f0f, 0 0 80px #f0f;
}

.neonSign2 {
  top: 40%;
  right: 8%;
  color: #0ff;
  text-shadow: 0 0 5px #0ff, 0 0 10px #0ff, 0 0 20px #0ff, 0 0 40px #0ff, 0 0 80px #0ff;
  animation-delay: -0.5s;
}

@keyframes flicker {
  0%, 18%, 22%, 25%, 53%, 57%, 100% {
    opacity: 1;
    text-shadow: 0 0 5px #f0f, 0 0 10px #f0f, 0 0 20px #f0f, 0 0 40px #f0f, 0 0 80px #f0f;
  }
  20%, 24%, 55% {
    opacity: 0.7;
    text-shadow: none;
  }
}

.vehicles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.vehicle {
  position: absolute;
  left: -10%;
  width: 40px;
  height: 2px;
  background: linear-gradient(to right, #ff00ff, #00ffff);
  box-shadow: 0 0 10px 2px #ff00ff;
  animation: fly linear infinite;
}

@keyframes fly {
  to {
    left: 110%;
  }
}
