import React from 'react';
import { render } from '@testing-library/react';
import DystopiaShapes from '@/components/animations/DystopiaShapes';

jest.mock('@/components/animations/DystopiaShapes.module.css', () => ({
  dystopiaContainer: 'dystopiaContainer',
  rain: 'rain',
  rainDrop: 'rainDrop',
  cityscape: 'cityscape',
  building: 'building',
  building1: 'building1',
  building2: 'building2',
  building3: 'building3',
  building4: 'building4',
  neonSigns: 'neonSigns',
  neonSign: 'neonSign',
  neonSign1: 'neonSign1',
  neonSign2: 'neonSign2',
  vehicles: 'vehicles',
  vehicle: 'vehicle',
}));

describe('DystopiaShapes', () => {
  it('renders correctly and matches snapshot', () => {
    const { container } = render(<DystopiaShapes />);
    expect(container).toMatchSnapshot();
  });

  it('renders the main container', () => {
    const { container } = render(<DystopiaShapes />);
    expect(container.querySelector('.dystopiaContainer')).toBeInTheDocument();
  });

  it('renders the correct number of rain drops', () => {
    const { container } = render(<DystopiaShapes />);
    expect(container.querySelectorAll('.rainDrop').length).toBe(100);
  });

  it('renders all four buildings', () => {
    const { container } = render(<DystopiaShapes />);
    expect(container.querySelectorAll('.building').length).toBe(4);
  });

  it('renders two neon signs', () => {
    const { container } = render(<DystopiaShapes />);
    expect(container.querySelectorAll('.neonSign').length).toBe(2);
  });

  it('renders the correct number of vehicles', () => {
    const { container } = render(<DystopiaShapes />);
    expect(container.querySelectorAll('.vehicle').length).toBe(5);
  });
});
