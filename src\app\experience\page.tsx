'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';
import { useDualTrackAudioPlayer } from '@/hooks/useDualTrackAudioPlayer';

import Timeline from '@/components/Timeline';
import ChoiceCorridor from '@/components/ChoiceCorridor';
import TimedTextDisplay from '@/components/TimedTextDisplay';
import VolumeControl from '@/components/VolumeControl';
import { AnimatePresence } from 'framer-motion';

import { Button } from '@/components/ui/button';
import { Play } from 'lucide-react';
import EvolvingBackground from '@/components/EvolvingBackground';
import CountdownTimer from '@/components/CountdownTimer';
import timelineData from '@/data/timeline.json';
import { Section, UserChoice, Era } from '@/types';

const AUDIO_UTOPIA = '/audio/Timeline_Utopia.mp3';
const AUDIO_DYSTOPIA = '/audio/Timeline_Dystopia.mp3';

const CHOICE_START_TIME = 480; // 8:00
const CHOICE_END_TIME = 540;   // 9:00
const VISUAL_IMPACT_TIME = 229; // 9 seconds of flight, landing 6 seconds before sound
const SOUND_IMPACT_TIME = 235;

const ExperiencePage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const isDevMode = searchParams.get('dev') === 'true';

  const [activeTrack, setActiveTrack] = useState(AUDIO_DYSTOPIA);
  const {
    isPlaying,
    currentTime,
    duration,
    volume,
    hasStarted,
    remainingTime,
    activeTrack: currentActiveTrack,
    isPreloading,
    preloadProgress,
    setVolume,
    play,
    changeSource,
    switchToTrack,
    preloadTracks,
    seek,
    cleanup
  } = useDualTrackAudioPlayer({
    initialSrc: activeTrack,
    utopiaTrack: AUDIO_UTOPIA,
    dystopiaTrack: AUDIO_DYSTOPIA,
  });

  const [userChoice, setUserChoice] = useState<UserChoice>('none');
  const [currentSection, setCurrentSection] = useState<Section | null>(null);
  const [currentEra, setCurrentEra] = useState<Era | null>(null);
  const [showChoice, setShowChoice] = useState(false);
  const [choiceMade, setChoiceMade] = useState(false);

  // Separate effect for choice window management and track preloading
  useEffect(() => {
    const isChoiceWindow = currentTime >= CHOICE_START_TIME && currentTime < CHOICE_END_TIME;
    setShowChoice(isChoiceWindow && !choiceMade);

    // Start preloading tracks when choice window begins
    if (currentTime >= CHOICE_START_TIME && !isPreloading && !preloadProgress.utopia && !preloadProgress.dystopia) {
      console.log('[Experience] Starting track preloading for choice window');
      preloadTracks(AUDIO_UTOPIA, AUDIO_DYSTOPIA).catch(console.error);
    }
  }, [currentTime, choiceMade, isPreloading, preloadProgress, preloadTracks]);

  // Separate effect for automatic choice selection
  useEffect(() => {
    if (currentTime >= CHOICE_END_TIME && !choiceMade) {
      const isUtopia = Math.random() < 0.5;
      const randomChoice = isUtopia ? AUDIO_UTOPIA : AUDIO_DYSTOPIA;

      // Batch state updates to prevent race conditions
      setUserChoice(isUtopia ? 'utopia' : 'dystopia');
      setChoiceMade(true);

      // Use seamless switching if available, otherwise fallback to traditional method
      try {
        const trackType = isUtopia ? 'utopia' : 'dystopia';
        const isPreloaded = isUtopia ? preloadProgress.utopia : preloadProgress.dystopia;

        if (isPreloaded) {
          console.log(`Auto-selecting ${trackType}: using seamless switch`);
          switchToTrack(trackType);
          setActiveTrack(randomChoice);
        } else {
          console.log(`Auto-selecting ${trackType}: using traditional change from ${activeTrack} to ${randomChoice}`);
          changeSource(randomChoice);
          setActiveTrack(randomChoice);
        }
      } catch (error) {
        console.error('Failed to change audio source during automatic selection:', error);
      }
    }
  }, [currentTime, choiceMade, activeTrack, changeSource, switchToTrack, preloadProgress]);

  // Separate effect for section and era management
  useEffect(() => {
    const section = timelineData.find(s => currentTime >= s.start && currentTime < s.end) || null;
    setCurrentSection(section);

    if (section) {
      const newEra: Era = {
        id: section.timedTextKey,
        subEra: section.timedTextKey === 'branchingFutures' && (userChoice === 'utopia' || userChoice === 'dystopia') ? userChoice : null,
      };
      if (JSON.stringify(newEra) !== JSON.stringify(currentEra)) {
        setCurrentEra(newEra);
      }
    } else {
      setCurrentEra(null);
    }
  }, [currentTime, userChoice, currentEra]);

  // Cleanup preloaded tracks after choice is made and confirmed
  useEffect(() => {
    if (choiceMade && currentTime > CHOICE_END_TIME + 5) { // 5 seconds after choice window ends
      console.log('[Experience] Cleaning up preloaded tracks');
      cleanup();
    }
  }, [choiceMade, currentTime, cleanup]);

  useEffect(() => {
    // Redirect to results page when the audio finishes
    if (duration > 0 && currentTime >= duration - 0.5) {
      router.push('/results');
    }
  }, [currentTime, duration, router]);

  useEffect(() => {
    // Expose seek function for tests in the development environment.
    if (process.env.NODE_ENV === 'development') {
      // Extend the Window interface for our custom function
      interface CustomWindow extends Window {
        seek?: (time: number, duration?: number) => void;
      }
      (window as CustomWindow).seek = seek;
    }
  }, [seek]);

  const submitVote = async (choice: 'utopia' | 'dystopia') => {
    try {
      const response = await fetch('/api/vote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ choice }),
      });
      if (!response.ok) {
        throw new Error('Vote submission failed');
      }
      console.log(`Vote for ${choice} submitted successfully.`);
    } catch (error) {
      console.error('Error submitting vote:', error);
    }
  };

  const handleSelectUtopia = () => {
    console.log('[Choice] Utopia chosen');
    console.log(`[Choice] Current activeTrack: ${activeTrack}`);
    console.log(`[Choice] Current audio time: ${currentTime}s`);

    // Prevent multiple clicks
    if (choiceMade) {
      console.log('[Choice] Choice already made, ignoring click');
      return;
    }

    // Batch all state updates together to prevent race conditions
    setUserChoice('utopia');
    setChoiceMade(true);
    setShowChoice(false);

    // Use seamless switching if tracks are preloaded, otherwise fallback to traditional method
    try {
      if (preloadProgress.utopia) {
        console.log('[Choice] Using seamless switch to utopia track');
        switchToTrack('utopia');
        setActiveTrack(AUDIO_UTOPIA);
        console.log('[Choice] Seamless switch to utopia completed');
      } else {
        console.log(`[Choice] Fallback: Initiating traditional audio change: ${activeTrack} -> ${AUDIO_UTOPIA}`);
        changeSource(AUDIO_UTOPIA);
        setActiveTrack(AUDIO_UTOPIA);
        console.log('[Choice] Traditional audio change initiated successfully');
      }
    } catch (error) {
      console.error('[Choice] Failed to change audio source to utopia:', error);
    }

    // Submit vote asynchronously without blocking
    submitVote('utopia').catch(console.error);
  };

  const handleSelectDystopia = () => {
    console.log('[Choice] Dystopia chosen');
    console.log(`[Choice] Current activeTrack: ${activeTrack}`);
    console.log(`[Choice] Current audio time: ${currentTime}s`);

    // Prevent multiple clicks
    if (choiceMade) {
      console.log('[Choice] Choice already made, ignoring click');
      return;
    }

    // Batch all state updates together to prevent race conditions
    setUserChoice('dystopia');
    setChoiceMade(true);
    setShowChoice(false);

    // Use seamless switching if tracks are preloaded, otherwise fallback to traditional method
    try {
      if (preloadProgress.dystopia) {
        console.log('[Choice] Using seamless switch to dystopia track');
        switchToTrack('dystopia');
        setActiveTrack(AUDIO_DYSTOPIA);
        console.log('[Choice] Seamless switch to dystopia completed');
      } else {
        console.log(`[Choice] Fallback: Initiating traditional audio change: ${activeTrack} -> ${AUDIO_DYSTOPIA}`);
        changeSource(AUDIO_DYSTOPIA);
        setActiveTrack(AUDIO_DYSTOPIA);
        console.log('[Choice] Traditional audio change initiated successfully');
      }
    } catch (error) {
      console.error('[Choice] Failed to change audio source to dystopia:', error);
    }

    // Submit vote asynchronously without blocking
    submitVote('dystopia').catch(console.error);
  };

  return (
    <div className="relative flex min-h-screen flex-col items-center justify-between p-12 text-white">
      <EvolvingBackground currentEra={currentEra} isPlaying={isPlaying} currentSection={currentSection} currentTime={currentTime} />

      <AnimatePresence>
        {showChoice && (
          <ChoiceCorridor
            onSelectUtopia={handleSelectUtopia}
            onSelectDystopia={handleSelectDystopia}
          />
        )}
      </AnimatePresence>

      <main className="flex-grow flex flex-col items-center justify-center z-10">
        <TimedTextDisplay
          currentTime={currentTime}
          currentSection={currentSection}
          userChoice={userChoice}
          isPlaying={isPlaying}
        />
      </main>

      <footer className="w-full flex flex-col items-center gap-6 z-20">
        <div className="w-full px-4">
          <Timeline
            currentTime={currentTime}
            duration={duration}
            sections={timelineData}
            onSeek={isDevMode ? seek : undefined}
          />
        </div>
        <div className="flex items-center gap-4">
          {!hasStarted ? (
            // Initial Play button - only shown before playback starts
            <div className="rounded-md p-px bg-gradient-to-b from-gray-300/25 to-gray-600/20">
              <Button
                onClick={play}
                size="lg"
                className="bg-black/50 backdrop-blur-sm border-0 hover:bg-white/10 rounded-[6px] h-10"
              >
                <Play className="mr-2 h-4 w-4" />
                Play
              </Button>
            </div>
          ) : (
            // Countdown timer - shown after playback starts
            <CountdownTimer remainingTime={remainingTime} />
          )}
          <VolumeControl volume={volume} setVolume={setVolume} />
        </div>
      </footer>
    </div>
  );
};

export default ExperiencePage;
